import dynamic from 'next/dynamic';
import {
  Layout,
  Like,
  Section,
  Card,
  FeaturedCategoryCoupons,
  AllCategories,
  AnnouncementBannerSection,
  TestedForYou
} from 'components';
import useAPIError from 'components/APIErrorNotification/useAPIError';
import { displayCouponCode } from 'components/Card/Card';
import LimitOverlay, {
  getOverlayMessage,
} from 'components/LimitOverlay/LimitOverlay';
import {
  isVideoTypeAccepted,
} from 'components/VideoBlock/VideoBlock';
import Head from 'next/head';
import Link from 'next/link';
import { useState } from 'react';
import { useSelector } from 'react-redux';
import NewsletterSection from 'sections/NewsletterSection';
import { isAuth } from 'services/auth.service';
import * as BrandService from 'services/brands.service';
import * as CategoryService from 'services/categories.service';
import * as ConsumerService from 'services/consumer.service';
import * as CouponService from 'services/coupons.service';
import * as SubscriptionService from 'services/subscription.service';
import * as HomepageService from 'services/homepage.service';
import { toggleCouponFavorite } from 'util/helpers';
import * as nodeHelpers from 'util/nodeHelpers';
import LandingPageMediaOutlets from '../components/LandingPageMediaOutlets/LandingPageMediaOutlets';
import SparooFrame from '../components/SparooFrame/SparooFrame';
import { HeroBannerCarousel, CouponOfWeek, PopularBrand } from '../components';
import { getCouponAltText } from '../util/helpers';

const BrandsCarouselSection = dynamic(() => import('sections/BrandsCarouselSection'));
const GenericModal = dynamic(() => import('components/ModalGeneric/ModalGeneric'));
const SubscriptionPaymentModal = dynamic(() => import('components/SettingsSegments/SubscriptionPaymentDialog'));
const SubscriptionPaymentErrorModal = dynamic(() => import('components/PaymentErrorModal/PaymentErrorModal'));

export async function getServerSideProps(context) {
  let customHeaders = null;
  try {
    customHeaders = await nodeHelpers.getCustomHeadersFromReq(
      context.req,
      context.res
    );
  } catch (error) {
    nodeHelpers.logout(context.res);
    return {};
  }

  const [
    hasValidSubscriptionResponse,
    subscriptionDataResponse,
    userDetailsResponse,
    homepageData
  ] = await Promise.allSettled([
    customHeaders ? SubscriptionService.checkValidSubscription(customHeaders) : null,
    customHeaders ? SubscriptionService.getSubscription(customHeaders) : null,
    customHeaders ? ConsumerService.getConsumer(customHeaders) : null,
    HomepageService.getAllHomepageData(customHeaders)
  ]);

  // Fallback to traditional methods if the new API endpoints aren't available yet
  let couponsOfWeek = homepageData.value?.couponOfTheWeek || [];

  // If we didn't get data from the new endpoints, use the old methods
  const allBrands = await BrandService.getBrands({
    page: 0,
    pageSize: 24,
    sort: [{ field: 'id', dir: 'ASC' }],
    search: '',
  });

  const allCoupons = await CouponService.getLatestCoupons(customHeaders);

  const categories = await CategoryService.getCategories({
    pageSize: 120,
  });

  return {
    props: {
      hasValidSubscription:
        hasValidSubscriptionResponse?.value?.data ||
        subscriptionDataResponse?.value?.data?.status === 'CANCELED' ||
        false,
      userDetails: userDetailsResponse?.value?.data || null,
      allBrands,
      allCoupons,
      couponsOfWeek,
      categories,
      bannerSliders: homepageData.value?.bannerSliders || null,
      popularBrands: homepageData.value?.popularEntities?.brands || [],
      popularCoupons: homepageData.value?.popularEntities?.coupons || [],
      testedForYou: homepageData.value?.testedForYou || [],
      textBanner: homepageData.value?.textBanner || {},
      homepageCategories: homepageData.value?.homepageCategories || [],
    },
  };
}

export default function Home({
  allBrands,
  allCoupons,
  couponsOfWeek,
  userDetails,
  hasValidSubscription,
  categories,
  bannerSliders,
  testedForYou,
  textBanner,
  popularBrands,
  popularCoupons,
  homepageCategories,
}) {

  const [subscriptionModal, setSubscriptionModal] = useState(false);
  const [videoModal, setVideoModal] = useState(false);
  const [policiesAcceptanceModal, setPoliciesAcceptanceModal] = useState(false);
  const [showPaymentErrorModal, setShowPaymentErrorModal] = useState(false);

  const [coupons, setCoupons] = useState(allCoupons);
  const [brands] = useState(allBrands);

  const { useCustomTheme, homePageTitle, homePageBodyText } = useSelector(
    (state) => state.settings
  );

  const { addMessage } = useAPIError();

  const isSignedIn = isAuth();

  const onPaymentError = () => {
    setShowPaymentErrorModal(true);
  };

  return (
    <>
      <Head>
        <title>CaptainCoupon – 100% Gutscheincode-Gültigkeitsgarantie</title>
        <meta
          name="description"
          content="Auf CaptainCoupon findest du nur gültige Gutscheincodes der besten
          Online-Shops & Marken! Jetzt Gratis-Mitgliedschaft sichern! &#9989;"
        />
        <meta
          name="facebook-domain-verification"
          content="h21x8yuq7d18yd6o0c8su5kc14zf79"
        />

        <meta property="og:url" content="https://captaincoupon.de/" />
        <meta property="og:type" content="website" />
        <meta property="og:title" content="CaptainCoupon – 100% Gutscheincode-Gültigkeitsgarantie" />
        <meta property="og:description" content="Auf CaptainCoupon findest du nur gültige Gutscheincodes der besten Online-Shops & Marken! Jetzt Gratis-Mitgliedschaft sichern! ✅" />
        <meta property="og:image" content="https://opengraph.b-cdn.net/production/documents/08b5ef98-f5a4-41bf-a32d-cb3874efdcfb.png?token=RM1Ko-a7LzylU9BPkYWIQi82kECRb__3DTKqIjBgjto&height=373&width=1000&expires=33245709390" />


        <meta name="twitter:card" content="summary_large_image" />
        <meta property="twitter:domain" content="captaincoupon.de" />
        <meta property="twitter:url" content="https://captaincoupon.de/" />
        <meta name="twitter:title" content="CaptainCoupon – 100% Gutscheincode-Gültigkeitsgarantie" />
        <meta name="twitter:description" content="Auf CaptainCoupon findest du nur gültige Gutscheincodes der besten Online-Shops & Marken! Jetzt Gratis-Mitgliedschaft sichern! ✅" />
        <meta name="twitter:image" content="https://opengraph.b-cdn.net/production/documents/08b5ef98-f5a4-41bf-a32d-cb3874efdcfb.png?token=RM1Ko-a7LzylU9BPkYWIQi82kECRb__3DTKqIjBgjto&height=373&width=1000&expires=33245709390"></meta>

        <link rel="preload" href="headers/CaptainCoupon-Header.png" as="image" />

        {/* Preconnect to Google Fonts */}
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link rel="preconnect" href="https://wb.captaincoupon.de" crossOrigin="anonymous" />

        {/* DNS Prefetch might be added as a fallback or for older browsers */}
        <link rel="dns-prefetch" href="https://fonts.gstatic.com" />
        <link rel="dns-prefetch" href="https://wb.captaincoupon.de" />

        <link rel="stylesheet" href="https://sibforms.com/forms/end-form/build/sib-styles.css"></link>
      </Head>

      <Layout home>
        <Section
          background="#81e9f0"
          rowClass="flex-row-reverse"
          customPadding={true}
          className="main-section overflow-hidden"
        >
          <div className="col-6 position-relative">
            {!useCustomTheme && (
              <>
                <a
                  href={!isSignedIn ? '/register' : '#'}
                  onClick={(event) => {
                    if (isSignedIn) {
                      event.preventDefault();
                      if (!hasValidSubscription) {
                        setSubscriptionModal(true);
                      }
                    }
                  }}
                >
                  <img
                    decoding="async"
                    src="headers/CaptainCoupon-Header.png"
                    className="home-header-image"
                    alt="Nur gültige Gutscheine & Rabatte für die beliebtesten Online-Shops und deine Marken"
                  />
                </a>
              </>
            )}
          </div>

          <div
            className={`${useCustomTheme ? 'col-12' : 'col-6'} text-content`}
          >
            {useCustomTheme ? (
              <>
                <h1 className="main-header landing-page-header">
                  {homePageTitle}
                </h1>
                <h4 className="subtitle-landing-page">{homePageBodyText}</h4>
              </>
            ) : (
              <div className='m-auto'>
                <h2 className="main-header landing-page-header">
                  Garantiert gültige Gutscheincodes und Rabatte
                </h2>
                <h4 className="subtitle-landing-page">
                  Spare mit exklusiven Gutscheincodes und erhalte die besten Rabatte und Deals unserer Markenpartner.
                </h4>
                {/* {!isSignedIn ? (
                  <Link href="register">
                    <a className="btn btn-dark hover-white">
                      Jetzt bei CC+ registrieren
                    </a>
                  </Link>
                ) : !hasValidSubscription ? (
                  <button
                    className="btn btn-dark hover-white"
                    onClick={() => setSubscriptionModal(true)}
                  >
                    Mitgliedschaft starten
                  </button>
                ) : null} */}
              </div>
            )}
          </div>
        </Section>

        {bannerSliders && bannerSliders.banners && bannerSliders.banners.length > 0 && (
          <Section
            className="pad-tb-4"
            customPadding={true}
            background="#fff"
          >
            <HeroBannerCarousel
              bannerSlider={bannerSliders}
            />
          </Section>
        )}

        {couponsOfWeek && couponsOfWeek.length > 0 && (
          <Section
            className="pad-lg-t-4 pad-lg-b-1 pad-t-1"
            background="#F8F8F8"
            customPadding={true}
          >
            <h2 className="col-12 section-header text-left justify-content-space-between px-1 px-md-3">
              <span>Coupons der Woche</span>
            </h2>
            <div className="col-12 px-1 px-md-3">
              <CouponOfWeek coupons={couponsOfWeek} />
            </div>
          </Section>
        )}

        {false && (
          <div
            className="pad-t-2 pad-lg-t-2 pad-b-2 row"
            style={{ background: '#F5F5F5' }}
          >
            <LandingPageMediaOutlets className={`col pt-lg-3 pb-lg-3`} />
          </div>
        )}

        {/*TODO re-enable iframe once issues with external content are fixed*/}
        {false && !useCustomTheme && (
          <Section
            className="pad-lg-t-8 pad-t-1"
            background="#FFFFFF"
            customPadding={true}
          >
            <div className="col">
              <SparooFrame />
            </div>
          </Section>
        )}

        <Section
          className="pad-lg-t-4 pad-lg-b-4 pad-t-1"
          background="#F8F8F8"
          customPadding={true}
        >
          <h1 className="col-12 section-header section-header__with-icon text-left justify-content-space-between px-1 px-md-3">
            <span>Beliebte Rabatte</span>
          </h1>

          <div className="col-sm-12">
            <div className="row">
              {popularCoupons &&
                popularCoupons?.map((item, index) => {
                  return (
                    <Card
                      className="col-6 col-lg-3 coupon-card-landing-page white-card px-1 px-md-3"
                      cardClassName="border-grey"
                      key={index}
                    >
                      <Card.Image
                        url={`/brand/${item.brandSlug}`}
                        src={item.compressedImage || item.image}
                        alt={getCouponAltText(item)}
                        eger={index < 4}
                      >
                        <Like
                          isLiked={item.isFavourite}
                          id={item.id}
                          onSuccess={() =>
                            setCoupons((coupons) =>
                              toggleCouponFavorite(coupons, item.id)
                            )
                          }
                          onError={(status, data) => {
                            addMessage(data, 'error');
                          }}
                        />
                        {item?.status !== 'NO_ACTIVE_SUBSCRIPTION' && (
                          <LimitOverlay message={getOverlayMessage(item)} className="border-rounded-top-14" />
                        )}
                        <Card.Discount
                          type={item.discountType}
                          shortDescription={item.shortDescription}
                        >
                          {item.discountValue}
                        </Card.Discount>
                      </Card.Image>
                      <Card.ClientLogo src={item?.brandLogo} alt={`Logo der Brand ${item.brandName || ''} gutschein`} />
                      <Card.Description>
                        {item.shortCouponDescription}
                      </Card.Description>
                      <Card.CouponButton
                        coupon={item}
                        getCouponCode={() => displayCouponCode(item.brandSlug)}
                      >
                        <p>{item.code ? 'Code kopieren' : 'Gutscheincode'}</p>
                      </Card.CouponButton>
                    </Card>
                  );
                })}
            </div>
          </div>
          <div className="col-sm-12 pad-lg-t-4 pad-lg-b-1 pad-t-1 pad-lg-b-4 pad-b-5 text-center">
            <Link href="/rabatte" className="btn btn-dark">
              Alle Rabatte
            </Link>
          </div>
        </Section>

        <Section
          className="pad-lg-t-4 pad-lg-b-4 pad-t-1"
          background="#fff"
          customPadding={true}
        >
          <h2 className="col-12 section-header text-left justify-content-space-between px-1 px-md-3">
            <span>Beliebte Marken</span>
          </h2>
          <div className="col-12">
            {popularBrands && popularBrands.length > 0 ? (
              <div className="row">
                {popularBrands.map((brand, index) => (
                  <div className="col-4 px-1 px-md-3" key={`brand-${brand.id || index}`}>
                    <PopularBrand brand={brand} />
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-4">Keine beliebten Marken verfügbar</div>
            )}
          </div>
        </Section>

        <AnnouncementBannerSection banner={textBanner} />

        {homepageCategories && homepageCategories.length > 0 &&
          homepageCategories.map((category, index) => (
            category.coupons && category.coupons.length > 0 && (
              <Section
                key={`category-section-${category.id || index}`}
                className={`pad-lg-t-4 ${(index + 1) === homepageCategories.length ? 'pad-lg-b-4' : 'pad-t-1'}`}
                background="#F8F8F8"
                customPadding={true}
              >
                <h2 className="col-12 section-header text-left justify-content-space-between px-1 px-md-3">
                  <span>{category.categoryName}</span>
                </h2>
                <div className="col-12 px-1 px-md-3">
                  <FeaturedCategoryCoupons coupons={category.coupons} />
                </div>
              </Section>
            )
          ))
        }

        <Section
          className="pad-lg-t-4"
          background="#F8F8F8"
          customPadding={true}
        >
          <h2 className="col-12 section-header text-left justify-content-space-between px-1 px-md-3">
            <span>Alle Kategorien</span>
          </h2>
          <div className="col-12 px-1 px-md-3">
            <AllCategories categories={categories} />
          </div>
        </Section>

        {testedForYou && testedForYou.length > 0 && (
          <Section
            className="pad-lg-t-4 pad-lg-b-4 pad-t-1"
            background="#FFFFFF"
            customPadding={true}
          >
            <TestedForYou items={testedForYou} />
          </Section>
        )}

        <NewsletterSection headerText="Verpasse keine neuen Coupons deiner Lieblingsmarken" />

        <Section
          className="pad-lg-t-4 pad-lg-b-4 pad-t-1"
          background="#F8F8F8"
          customPadding={true}
        >
          <h1 className="col-12 section-header section-header__with-icon text-left justify-content-space-between px-1 px-md-3">
            <span>Bald abgelaufen</span>
          </h1>

          <div className="col-sm-12">
            <div className="row">
              {popularCoupons &&
                popularCoupons?.map((item, index) => {
                  return (
                    <Card
                      className="col-6 col-lg-3 coupon-card-landing-page white-card px-1 px-md-3"
                      cardClassName="border-grey"
                      key={index}
                    >
                      <Card.Image
                        url={`/brand/${item.brandSlug}`}
                        src={item.compressedImage || item.image}
                        alt={getCouponAltText(item)}
                        eger={index < 4}
                      >
                        <Like
                          isLiked={item.isFavourite}
                          id={item.id}
                          onSuccess={() =>
                            setCoupons((coupons) =>
                              toggleCouponFavorite(coupons, item.id)
                            )
                          }
                          onError={(status, data) => {
                            addMessage(data, 'error');
                          }}
                        />
                        {item?.status !== 'NO_ACTIVE_SUBSCRIPTION' && (
                          <LimitOverlay message={getOverlayMessage(item)} className="border-rounded-top-14" />
                        )}
                        <Card.Discount
                          type={item.discountType}
                          shortDescription={item.shortDescription}
                        >
                          {item.discountValue}
                        </Card.Discount>
                      </Card.Image>
                      <Card.ClientLogo src={item?.brandLogo} alt={`Logo der Brand ${item.brandName || ''} gutschein`} />
                      <Card.Description>
                        {item.shortCouponDescription}
                      </Card.Description>
                      <Card.CouponButton
                        coupon={item}
                        getCouponCode={() => displayCouponCode(item.brandSlug)}
                      >
                        <p>{item.code ? 'Code kopieren' : 'Gutscheincode'}</p>
                      </Card.CouponButton>
                    </Card>
                  );
                })}
            </div>
          </div>
        </Section>

        {brands && brands.length > 0 && (
          <BrandsCarouselSection
            brands={brands}
            className="pad-tb-4"
            landingPage={true}
          />
        )}

        <GenericModal
          modal={subscriptionModal}
          title="Kasse"
          className="cancel-sub-modal"
          customPadding={true}
          displayCloseButton={true}
          onClose={() => setSubscriptionModal(false)}
        >
          <SubscriptionPaymentModal
            consumer={userDetails}
            onSuccess={() => {
              window.location.reload();
            }}
            onPaymentError={onPaymentError}
            onClose={() => setSubscriptionModal(false)}
          />
        </GenericModal>

        <SubscriptionPaymentErrorModal
          show={showPaymentErrorModal}
          onClose={() => setShowPaymentErrorModal(false)}
        />
      </Layout>
    </>
  );
}